import { IntentDev } from '@flow-editor-v1/model';
import { axiosClient } from './axiosClient';

export const intentDevApi = {
  getIntentDev(): Promise<Array<IntentDev>> {
    const url = `/v2/intent_dev/list`;
    return axiosClient.post(url, {});
  },

  getIntentDevAvailable(): Promise<Array<IntentDev>> {
    const url = `/v2/intent_dev/list/available`;
    return axiosClient.post(url, {});
  },
};
