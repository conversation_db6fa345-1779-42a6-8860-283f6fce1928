// @ts-nocheck
import { useEffect, useState } from 'react';

export const useDeviceSupport = () => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  const ua = navigator.userAgent.toLowerCase();
  const isMac =
    ua.includes('macintosh') ||
    ua.includes('ipad') ||
    ua.includes('iphone') ||
    ua.includes('ipod');

  const controlKeyCode = isMac ? ['Meta', 'Control'] : ['Control'];

  useEffect(() => {
    const isTouch =
      window.matchMedia('(any-pointer: coarse)').matches &&
      !window.matchMedia('(any-pointer: fine)').matches;

    setIsTouchDevice(isTouch);
  }, []);

  const isCtrlKeyPressed = (e) => {
    return isMac ? e.metaKey || e.ctrlKey : e.ctrlKey;
  };

  return {
    isTouchDevice,
    isMacOs: isMac,
    controlKeyCode,
    isCtrlKeyPressed,
  };
};
