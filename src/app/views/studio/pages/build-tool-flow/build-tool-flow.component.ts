import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  inject,
  input,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core';
import { Router } from '@angular/router';
import {
  AGENT_FLOW_PATH,
  APP_ROUTES,
  STUDIO_PATH,
  TRIGGER_KEYS,
} from '@core/constants';
import { TriggerService } from '@core/services';
import { StudioStore, UIStore } from '@core/stores';
import EditorFlowView from '@views/flow-editor/views/EditorFlowView';
import * as React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-build-tool-flow',
  standalone: true,
  imports: [],
  templateUrl: './build-tool-flow.component.html',
  styleUrl: './build-tool-flow.component.css',
})
export class BuildToolFlowComponent
  implements OnInit, AfterViewInit, OnDestroy
{
  toolId = input.required<number>();

  rootId: string = 'flow-canvas';
  root: Root | undefined;
  protected destroy$ = new Subject<void>();

  // Debug panel state (separate from existing debug mode)
  debugPanelVisible = signal(false);

  private messageListener!: (event: MessageEvent) => void;

  private router: Router = inject(Router);
  private el: ElementRef = inject(ElementRef);
  private studioStore = inject(StudioStore);
  private uiStore = inject(UIStore);
  private triggerService = inject(TriggerService);

  private previousStatus: string | null = this.studioStore.status();

  private studioStoreEffect = effect(() => {
    const currentStatus = this.studioStore.status();
    if (currentStatus !== this.previousStatus) {
      this.previousStatus = currentStatus;
      void this.router.navigate(['/studio/build']);
    }
  });

  private themeEffect = effect(() => {
    const currentTheme = this.uiStore.theme();
    postMessage({
      type: 'theme',
      data: { theme: currentTheme },
    });
  });

  ngOnInit(): void {
    this.messageListener = (event) => {
      if (event && event?.data && event?.data.type === 'debug_mode') {
        this.studioStore.setFlowDebugMode(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'toggle_debug_panel') {
        console.log('Received toggle_debug_panel message:', event.data.data);
        this.debugPanelVisible.set(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'back_to_agent_tool') {
        void this.router.navigate(
          [
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.AGENT_TOOL_MAPPING}`,
          ],
          {
            queryParams: {
              agent: '',
              tool: '',
            },
          }
        );
      }
    };
    window.addEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'false');
  }

  ngAfterViewInit(): void {
    this.render();
    postMessage({
      type: 'studio_status',
      data: { status: this.studioStore.status() },
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.root) {
      this.root.unmount();
      this.root = undefined;
    }

    // Reset debug mode when component is destroyed
    this.studioStore.setFlowDebugMode(false);

    // Reset debug panel state
    this.debugPanelVisible.set(false);

    // Remove message listener
    window.removeEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'true');
  }

  closeDebugPanel(): void {
    console.log('Closing debug panel');
    this.debugPanelVisible.set(true);
    // Notify React component that debug panel was closed
    window.postMessage({
      type: 'debug_panel_closed',
      data: false
    }, '*');
  }

  private render() {
    if (this.toolId()) {
      const reactRoot = this.el.nativeElement.querySelector(`#${this.rootId}`);
      if (reactRoot) {
        if (this.root) {
          this.root.unmount();
        }

        this.root = createRoot(reactRoot);
        this.root.render(
          React.createElement(EditorFlowView, { flowId: this.toolId() })
        );

        postMessage({
          type: 'theme',
          data: { theme: this.uiStore.theme() },
        });
      }
    }
  }
}
