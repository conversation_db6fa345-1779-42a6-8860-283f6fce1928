import {
  AfterViewInit,
  Component,
  effect,
  ElementRef,
  inject,
  input,
  On<PERSON><PERSON>roy,
  OnInit,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  AGENT_FLOW_PATH,
  APP_ROUTES,
  STUDIO_PATH,
  TRIGGER_KEYS,
} from '@core/constants';
import { TriggerService } from '@core/services';
import { StudioStore, UIStore } from '@core/stores';
import EditorFlowView from '@views/flow-editor/views/EditorFlowView';
import * as React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-build-tool-flow',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './build-tool-flow.component.html',
  styleUrl: './build-tool-flow.component.css',
})
export class BuildToolFlowComponent
  implements OnInit, AfterViewInit, On<PERSON><PERSON>roy
{
  toolId = input.required<number>();

  rootId: string = 'flow-canvas';
  root: Root | undefined;
  protected destroy$ = new Subject<void>();

  // Debug panel state (separate from existing debug mode)
  debugPanelVisible = signal(false);

  // Mock debug data with expand/collapse state
  debugData = signal([
    {
      id: 1,
      time: '2024-01-15 14:32:15.234',
      title: 'Tool Flow Initialization',
      content: 'Flow ID: 12345\nStatus: Starting\nConfiguration loaded successfully\nInput parameters validated\nMemory allocated: 256MB',
      expanded: false
    },
    {
      id: 2,
      time: '2024-01-15 14:32:16.567',
      title: 'API Request - User Authentication',
      content: 'POST /api/auth/validate\nHeaders: Authorization: Bearer ***\nPayload: { userId: "user123", sessionId: "sess456" }\nResponse: 200 OK\nExecution time: 145ms',
      expanded: false
    },
    {
      id: 3,
      time: '2024-01-15 14:32:17.890',
      title: 'Data Processing Step 1',
      content: 'Processing input data...\nRecords processed: 1,247\nValidation passed: 1,245\nValidation failed: 2\nProcessing time: 2.3s\nMemory usage: 312MB',
      expanded: false
    },
    {
      id: 4,
      time: '2024-01-15 14:32:20.123',
      title: 'External API Call - Data Enrichment',
      content: 'GET /api/external/enrich\nQuery params: { type: "user_data", batch_size: 100 }\nResponse: 200 OK\nData enriched: 1,245 records\nAPI rate limit remaining: 4,755/5,000\nExecution time: 2.1s',
      expanded: false
    },
    {
      id: 5,
      time: '2024-01-15 14:32:22.456',
      title: 'Warning - Performance Threshold',
      content: 'Performance warning detected\nCurrent processing time: 7.2s\nThreshold: 5.0s\nRecommendation: Consider optimizing data queries\nAffected operations: data_enrichment, validation_step',
      expanded: false
    },
    {
      id: 6,
      time: '2024-01-15 14:32:25.789',
      title: 'Database Transaction',
      content: 'BEGIN TRANSACTION\nINSERT INTO processed_data (1,245 records)\nUPDATE user_stats SET last_processed = NOW()\nCOMMIT\nTransaction time: 1.8s\nRows affected: 1,246',
      expanded: false
    },
    {
      id: 7,
      time: '2024-01-15 14:32:27.012',
      title: 'Error - Retry Mechanism Triggered',
      content: 'Error: Connection timeout to external service\nService: payment-processor\nAttempt: 1/3\nRetrying in 2s...\nBackoff strategy: exponential\nNext retry: 2024-01-15 14:32:29.012',
      expanded: false
    },
    {
      id: 8,
      time: '2024-01-15 14:32:29.345',
      title: 'API Request - Payment Processing (Retry)',
      content: 'POST /api/payments/process\nAttempt: 2/3\nPayload: { amount: 99.99, currency: "USD", method: "card" }\nResponse: 200 OK\nTransaction ID: txn_789xyz\nExecution time: 892ms',
      expanded: false
    },
    {
      id: 9,
      time: '2024-01-15 14:32:30.678',
      title: 'State Change - Flow Completion',
      content: 'Flow state changed: PROCESSING → COMPLETED\nTotal execution time: 15.4s\nRecords processed: 1,245\nSuccess rate: 99.84%\nErrors encountered: 2\nMemory peak: 387MB\nCleanup initiated',
      expanded: false
    },
    {
      id: 10,
      time: '2024-01-15 14:32:31.901',
      title: 'Cleanup and Finalization',
      content: 'Temporary files removed: 12\nMemory released: 387MB\nConnections closed: 5\nLog files archived\nFlow execution summary generated\nStatus: SUCCESS',
      expanded: false
    }
  ]);

  private messageListener!: (event: MessageEvent) => void;

  private router: Router = inject(Router);
  private el: ElementRef = inject(ElementRef);
  private studioStore = inject(StudioStore);
  private uiStore = inject(UIStore);
  private triggerService = inject(TriggerService);

  private previousStatus: string | null = this.studioStore.status();

  private studioStoreEffect = effect(() => {
    const currentStatus = this.studioStore.status();
    if (currentStatus !== this.previousStatus) {
      this.previousStatus = currentStatus;
      void this.router.navigate(['/studio/build']);
    }
  });

  private themeEffect = effect(() => {
    const currentTheme = this.uiStore.theme();
    postMessage({
      type: 'theme',
      data: { theme: currentTheme },
    });
  });

  ngOnInit(): void {
    this.messageListener = (event) => {
      if (event && event?.data && event?.data.type === 'debug_mode') {
        this.studioStore.setFlowDebugMode(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'toggle_debug_panel') {
        console.log('Received toggle_debug_panel message:', event.data.data);
        this.debugPanelVisible.set(event.data.data);
      }
      if (event && event?.data && event?.data.type === 'back_to_agent_tool') {
        void this.router.navigate(
          [
            `${APP_ROUTES.STUDIO}/${STUDIO_PATH.BUILDER}/${STUDIO_PATH.AGENT_FLOW}/${AGENT_FLOW_PATH.AGENT_TOOL_MAPPING}`,
          ],
          {
            queryParams: {
              agent: '',
              tool: '',
            },
          }
        );
      }
    };
    window.addEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'false');
  }

  ngAfterViewInit(): void {
    this.render();
    postMessage({
      type: 'studio_status',
      data: { status: this.studioStore.status() },
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    if (this.root) {
      this.root.unmount();
      this.root = undefined;
    }

    // Reset debug mode when component is destroyed
    this.studioStore.setFlowDebugMode(false);

    // Reset debug panel state
    this.debugPanelVisible.set(false);

    // Remove message listener
    window.removeEventListener('message', this.messageListener);

    this.triggerService.trigger(TRIGGER_KEYS.FLOW_UNMOUNTED, 'true');
  }

  closeDebugPanel(): void {
    console.log('Closing debug panel');
    this.debugPanelVisible.set(false);
    // Notify React component that debug panel was closed
    window.postMessage({
      type: 'debug_panel_closed',
      data: false
    }, '*');
  }

  toggleDebugItem(itemId: number): void {
    this.debugData.update(items =>
      items.map(item =>
        item.id === itemId
          ? { ...item, expanded: !item.expanded }
          : item
      )
    );
  }

  // Method to simulate adding new debug entries (for testing)
  addDebugEntry(title: string, content: string): void {
    const newEntry = {
      id: Date.now(),
      time: new Date().toISOString().replace('T', ' ').substring(0, 23),
      title,
      content,
      expanded: false
    };

    this.debugData.update(items => [newEntry, ...items]);
  }

  private render() {
    if (this.toolId()) {
      const reactRoot = this.el.nativeElement.querySelector(`#${this.rootId}`);
      if (reactRoot) {
        if (this.root) {
          this.root.unmount();
        }

        this.root = createRoot(reactRoot);
        this.root.render(
          React.createElement(EditorFlowView, { flowId: this.toolId() })
        );

        postMessage({
          type: 'theme',
          data: { theme: this.uiStore.theme() },
        });
      }
    }
  }
}
