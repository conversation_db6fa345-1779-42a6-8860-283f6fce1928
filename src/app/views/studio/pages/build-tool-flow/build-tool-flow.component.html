<!-- Component Container with Positioning Context -->
<div class="build-tool-flow-container">
  <div [id]="rootId"></div>
  <!-- Debug Panel Overlay -->
  <div
    *ngIf="debugPanelVisible()"
    class="debug-panel-overlay"
    [class.slide-out]="debugPanelSlideOut()"
  >
    <div class="debug-panel-header">
      <h3>Debug Panel</h3>
      <button
        class="debug-panel-close"
        (click)="closeDebugPanel()"
      >
        ×
      </button>
    </div>
    <div class="debug-panel-content">
      <div class="debug-header">
        <h4>Tool Flow Debug Log</h4>
        <span class="debug-count">{{ debugData().length }} entries</span>
      </div>

      <div class="debug-list">
        <div
          *ngFor="let item of debugData()"
          class="debug-item"
          [class.expanded]="item.expanded"
        >
          <div class="debug-item-header" (click)="toggleDebugItem(item.id)">
            <div class="debug-item-info">
              <span class="debug-time">{{ item.time }}</span>
              <span class="debug-title">{{ item.title }}</span>
            </div>
            <button class="debug-expand-btn" [class.expanded]="item.expanded">
              <span class="expand-icon">▼</span>
            </button>
          </div>

          <div class="debug-item-content" *ngIf="item.expanded">
            <pre>{{ item.content }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
