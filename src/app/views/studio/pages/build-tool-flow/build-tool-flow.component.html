<!-- Component Container with Positioning Context -->
<div class="build-tool-flow-container">
  <div [id]="rootId"></div>

  <!-- Temporary Debug Controls -->
  <div style="position: absolute; top: 10px; right: 10px; z-index: 10000; background: white; padding: 10px; border: 1px solid #ccc;">
    <p>Debug Panel Visible: {{ debugPanelVisible() }}</p>
    <button (click)="debugPanelVisible.set(!debugPanelVisible())" style="padding: 5px 10px; background: #7241FF; color: white; border: none; border-radius: 4px; cursor: pointer;">
      Toggle Debug Panel (Manual)
    </button>
  </div>

  <!-- Debug Panel Overlay -->
  <div
    *ngIf="debugPanelVisible()"
    class="debug-panel-overlay"
  >
    <div class="debug-panel-header">
      <h3>Debug Panel</h3>
      <button
        class="debug-panel-close"
        (click)="closeDebugPanel()"
      >
        ×
      </button>
    </div>
    <div class="debug-panel-content">
      <p>Debug information will be displayed here...</p>
      <p>This panel is contained within the build-tool-flow component boundaries.</p>
      <p>Height: 40% of component height</p>
      <p>Position: Absolute within component container</p>
      <!-- Add your debug content here -->
    </div>
  </div>
</div>
