<!-- Component Container with Positioning Context -->
<div class="build-tool-flow-container">
  <div [id]="rootId"></div>

  <!-- Temporary Debug Controls -->
  <div style="position: absolute; top: 10px; right: 10px; z-index: 10000; background: white; padding: 10px; border: 1px solid #ccc; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
    <p style="margin: 0 0 8px 0; font-size: 12px;">Debug Panel: {{ debugPanelVisible() ? 'Visible' : 'Hidden' }}</p>
    <div style="display: flex; flex-direction: column; gap: 4px;">
      <button (click)="debugPanelVisible.set(!debugPanelVisible())" style="padding: 5px 10px; background: #7241FF; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;">
        Toggle Panel
      </button>
      <button (click)="addTestApiEntry()" style="padding: 5px 10px; background: #10b981; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;">
        Add Entry
      </button>
    </div>
  </div>

  <!-- Debug Panel Overlay -->
  <div
    *ngIf="debugPanelVisible()"
    class="debug-panel-overlay"
  >
    <div class="debug-panel-header">
      <h3>Debug Panel</h3>
      <button
        class="debug-panel-close"
        (click)="closeDebugPanel()"
      >
        ×
      </button>
    </div>
    <div class="debug-panel-content">
      <div class="debug-header">
        <h4>Tool Flow Debug Log</h4>
        <span class="debug-count">{{ debugData().length }} entries</span>
      </div>

      <div class="debug-list">
        <div
          *ngFor="let item of debugData()"
          class="debug-item"
          [class.expanded]="item.expanded"
        >
          <div class="debug-item-header" (click)="toggleDebugItem(item.id)">
            <div class="debug-item-info">
              <span class="debug-time">{{ item.time }}</span>
              <span class="debug-title">{{ item.title }}</span>
            </div>
            <button class="debug-expand-btn" [class.expanded]="item.expanded">
              <span class="expand-icon">▼</span>
            </button>
          </div>

          <div class="debug-item-content" *ngIf="item.expanded">
            <pre>{{ item.content }}</pre>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
