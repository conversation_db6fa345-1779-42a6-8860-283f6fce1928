/* Debug Panel Overlay Styles */
.debug-panel-overlay {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40vh;
  background: rgba(255, 255, 255, 0.98);
  border-top: 2px solid #7241FF;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

:host-context(.dark) .debug-panel-overlay {
  background: rgba(16, 24, 40, 0.98);
  border-top-color: #7241FF;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.debug-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

:host-context(.dark) .debug-panel-header {
  border-bottom-color: #374151;
  background: #1f2937;
}

.debug-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

:host-context(.dark) .debug-panel-header h3 {
  color: #f9fafb;
}

.debug-panel-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.debug-panel-close:hover {
  background: #f3f4f6;
  color: #374151;
}

:host-context(.dark) .debug-panel-close {
  color: #9ca3af;
}

:host-context(.dark) .debug-panel-close:hover {
  background: #374151;
  color: #f3f4f6;
}

.debug-panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  color: #374151;
}

:host-context(.dark) .debug-panel-content {
  color: #d1d5db;
}
