<div class="flex flex-col h-full text-light-text dark:text-dark-text bg-light-background dark:bg-dark-background">
  <!-- Dialog Header -->
  <div class="header border-b border-b-gray-200 pt-6 px-6 flex justify-between items-center pb-3">
    <div class="text-2xl font-bold card-title capitalize">
      Agent Details
    </div>
    <div class="hover:cursor-pointer">
      <app-custom-icon iconName="heroXCircle" class="text-2xl text-light-text dark:text-dark-text" (click)="close()"></app-custom-icon>
    </div>
  </div>

  <!-- Dialog Content -->
  <div class="w-full px-6 pt-6 content overflow-auto max-h-[75vh]">
    <div class="border-gray-200 dark:border-gray-500 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background p-4">
      <!-- Name Field -->
      <div class="mb-6">
        <label class="font-semibold">Name</label>
        <div class="mt-2 px-4 py-2 bg-light-background dark:bg-dark-background border border-gray-200 dark:border-gray-700 rounded-lg">
          {{ agent.name }}
        </div>
      </div>

      <!-- Description Field -->
      <div class="mb-6">
        <label class="font-semibold">Description</label>
        <div class="mt-2 px-4 py-2 bg-light-background dark:bg-dark-background border border-gray-200 dark:border-gray-700 rounded-lg min-h-[6em]">
          {{ agent.description }}
        </div>
      </div>

      <!-- Instruction Field -->
      <div class="mb-6">
        <label class="font-semibold">Instruction</label>
        <div class="mt-2 px-4 py-2 bg-light-background dark:bg-dark-background border border-gray-200 dark:border-gray-700 rounded-lg min-h-[10em]">
          {{ agent.instruction }}
        </div>
      </div>

      <!-- Rule Field -->
      <div class="mb-6">
        <label class="font-semibold">Rule</label>
        <div class="mt-2 px-4 py-2 bg-light-background dark:bg-dark-background border border-gray-200 dark:border-gray-700 rounded-lg min-h-[10em]">
          {{ agent.rule }}
        </div>
      </div>

      <!-- AI Model Field -->
      <div class="mb-6">
        <label class="font-semibold">AI Model</label>
        <div class="mt-2 px-4 py-2 bg-light-background dark:bg-dark-background border border-gray-200 dark:border-gray-700 rounded-lg">
          {{ agent.ai_model }}
        </div>
      </div>
    </div>
  </div>

  <!-- Dialog Footer -->
  <div class="footer p-6 flex justify-end">
    <button class="cursor-pointer h-[40px] min-w-[120px] px-3 rounded-2xl bg-light-secondary-background dark:bg-dark-secondary-background border border-gray-200 dark:border-0"
            (click)="close()">
      <span>Close</span>
    </button>
  </div>
</div>
