import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { APP_ROUTES } from '@core/constants';
import { UIStore } from '@core/stores';
import { DxButton } from '@dx-ui/ui';
import { SvgIconComponent } from '@shared/components';

@Component({
  selector: 'app-error',
  standalone: true,
  imports: [CommonModule, DxButton, SvgIconComponent],
  templateUrl: './error.component.html',
  styleUrl: './error.component.css',
})
export class ErrorComponent {
  route = inject(Router);
  uiStore = inject(UIStore);

  goToHome() {
    this.route.navigate([APP_ROUTES.DASHBOARD]);
  }
}
