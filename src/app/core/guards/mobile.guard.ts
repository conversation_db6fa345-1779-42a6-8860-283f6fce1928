import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { inject } from '@angular/core';
import { CanActivateFn, Router, RouterStateSnapshot } from '@angular/router';
import { APP_ROUTES } from '@core/constants';
import { map, tap } from 'rxjs';

export const mobileGuard: CanActivateFn = (_, state: RouterStateSnapshot) => {
  const router: Router = inject(Router);
  const breakpointObserver: BreakpointObserver = inject(BreakpointObserver);

  return breakpointObserver
    .observe([Breakpoints.Handset, Breakpoints.Tablet])
    .pipe(
      tap((large) => {
        if (!large.matches) {
          void router.navigate([APP_ROUTES.DASHBOARD]);
        }
      }),
      map((large) => large.matches)
    );
};
