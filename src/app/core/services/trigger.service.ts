import { Injectable, signal, WritableSignal } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class TriggerService {
  private triggers = new Map<string, WritableSignal<any>>();

  get(key: string) {
    if (!this.triggers.has(key)) {
      this.triggers.set(key, signal<any>(null));
    }
    return this.triggers.get(key)!;
  }

  trigger(key: string, value: any) {
    this.get(key)!.set(value);
  }

  reset(key: string) {
    this.get(key)!.set(false);
  }
}
