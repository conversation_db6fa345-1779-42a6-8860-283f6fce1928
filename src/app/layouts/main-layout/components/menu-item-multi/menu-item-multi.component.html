<div
  class="flex items-center justify-between gap-3 p-2 rounded-xl cursor-pointer bg-transparent text-neutral-content dark:text-dark-neutral-content hover:bg-base-400 dark:hover:bg-dark-base-400 hover:text-base-content dark:hover:text-dark-base-content"
  (click)="showItem()"
>
  <div class="flex items-center gap-3">
    @let icon = menuItem().icon; @if (icon) {
    <app-svg-icon
      [type]="icon"
      class="w-6 h-6 text-base-content dark:text-dark-base-content"
    ></app-svg-icon>
    } @else {
    <div class="w-6"></div>
    }
    <span
      class="flex-1 text-left rtl:text-right whitespace-nowrap text-[15px]"
      >{{ menuItem().title }}</span
    >
  </div>
  <ng-icon
    [@iconAnimation]="iconState()"
    name="heroChevronRightMini"
    size="24"
  ></ng-icon>
</div>

@if (showTemplate()) {
<div class="ml-1.5" [@slideDownUp]>
  <ng-container
    *ngTemplateOutlet="templateItem; context: { data: menuItem().children }"
  ></ng-container>
</div>
}

<ng-template #templateItem let-data="data">
  @for (item of data; track item.id) { @if (item.children) {
  <div class="flex items-center">
    <app-svg-icon
      type="icVerticalLine"
      class="w-6 h-10 !text-primary-border dark:!text-dark-primary-border"
    ></app-svg-icon>
    <app-menu-item-multi
      *hasRoles="{ userRoles: item?.roles ?? [], aiRoles: item?.rolesAI ?? [] }"
      [menuItem]="item"
      class="flex-1"
    ></app-menu-item-multi>
  </div>
  } @else {
  <div class="flex items-center">
    <app-svg-icon
      type="icVerticalLine"
      class="w-6 h-10 !text-primary-border dark:!text-dark-primary-border"
    ></app-svg-icon>
    <app-menu-item
      *hasRoles="{ userRoles: item?.roles ?? [], aiRoles: item?.rolesAI ?? [] }"
      [menuItem]="item"
      class="flex-1"
    ></app-menu-item>
  </div>
  } }
</ng-template>
