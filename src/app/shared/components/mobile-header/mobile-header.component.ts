import { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';
import {
  Component,
  computed,
  contentChild,
  DestroyRef,
  effect,
  inject,
  input,
  OnInit,
  signal,
  viewChild,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { NgIconsModule, provideIcons } from '@ng-icons/core';
import {
  heroChevronLeft,
  heroEllipsisVertical,
} from '@ng-icons/heroicons/outline';
import {
  ClickOutsideDirective,
  MHeaderCenterDirective,
  MHeaderLeftDirective,
  MHeaderRightDirective,
} from '@shared/directives';
import { debounceTime, fromEvent, Subscription } from 'rxjs';
import { AISelectComponent } from '../ai-select/ai-select.component';

@Component({
  selector: 'app-mobile-header',
  standalone: true,
  imports: [
    MHeaderLeftDirective,
    MHeaderCenterDirective,
    MHeaderRightDirective,
    ClickOutsideDirective,
    AISelectComponent,
    OverlayModule,
    NgIconsModule,
  ],
  templateUrl: './mobile-header.component.html',
  host: {
    style: '!fixed() && padding-top: 0',
  },
  providers: [provideIcons({ heroChevronLeft, heroEllipsisVertical })],
})
export class MobileHeaderComponent implements OnInit {
  transparent = input<boolean>(false);
  main = input<boolean>(false);
  border = input<boolean>(false);
  title = input<string>('');
  fixed = input<boolean>(true);
  hideMenu = input<boolean>(false);
  hideBack = input<boolean>(false);
  backFn = input<(() => void) | undefined>();

  left = contentChild<MHeaderLeftDirective>(MHeaderLeftDirective);
  center = contentChild<MHeaderCenterDirective>(MHeaderCenterDirective);
  right = contentChild<MHeaderRightDirective>(MHeaderRightDirective);

  trigger = viewChild<CdkOverlayOrigin>('trigger');

  isMenuOpened = signal<boolean>(false);
  collapseHeader = signal<boolean>(false);

  showBackButton = computed(() => !this.hideBack() && !this.left);

  scroll$: Subscription | undefined;

  private router = inject(Router);
  private destroyRef = inject(DestroyRef);

  constructor() {
    effect(() => {
      if (this.transparent()) {
        this.listenScroll();
      }
    });
  }

  ngOnInit(): void {
    this.listenScroll();
  }

  mBack() {
    const backFunction = this.backFn();
    if (typeof backFunction === 'function') {
      backFunction();
    } else {
      this._backFn();
    }
  }

  mMenu() {
    this.isMenuOpened.set(true);
  }

  private listenScroll() {
    fromEvent(window, 'scroll', { passive: true })
      .pipe(debounceTime(10), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        if (this.transparent()) {
          this.collapseHeader.set(window.scrollY > 70);
        }
      });
  }

  private _backFn() {
    if (window?.history?.length > 1) {
      window.history.back();
    } else {
      void this.router.navigateByUrl('/');
    }
  }
}
