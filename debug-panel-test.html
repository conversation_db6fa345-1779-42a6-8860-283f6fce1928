<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Panel Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .main-content {
            height: 80vh;
            background: white;
            border: 1px solid #ccc;
            padding: 20px;
            position: relative;
        }
        
        .debug-button {
            padding: 10px 20px;
            background: #7241FF;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
        }
        
        .debug-button.active {
            background: #5a2fd4;
            box-shadow: 0 0 10px rgba(114, 65, 255, 0.5);
        }
        
        /* Debug Panel Overlay Styles */
        .debug-panel-overlay {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40vh;
            background: rgba(255, 255, 255, 0.98);
            border-top: 2px solid #7241FF;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            display: flex;
            flex-direction: column;
        }
        
        .debug-panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }
        
        .debug-panel-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }
        
        .debug-panel-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6b7280;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s;
        }
        
        .debug-panel-close:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .debug-panel-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            color: #374151;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <h1>Debug Panel Test</h1>
        <p>This is a test to verify the debug panel functionality.</p>
        
        <button id="debugButton" class="debug-button">Debug Panel</button>
        
        <p>Click the "Debug Panel" button to toggle the debug panel overlay.</p>
        <p>The panel should appear at the bottom of the screen taking 40% of viewport height.</p>
    </div>
    
    <!-- Debug Panel Overlay -->
    <div id="debugPanel" class="debug-panel-overlay hidden">
        <div class="debug-panel-header">
            <h3>Debug Panel</h3>
            <button id="closeButton" class="debug-panel-close">×</button>
        </div>
        <div class="debug-panel-content">
            <p>Debug information will be displayed here...</p>
            <p>Panel height: 40vh</p>
            <p>Position: Fixed at bottom</p>
            <p>Z-index: 9999</p>
            <p>This panel should overlay on top of all other content.</p>
        </div>
    </div>
    
    <script>
        const debugButton = document.getElementById('debugButton');
        const debugPanel = document.getElementById('debugPanel');
        const closeButton = document.getElementById('closeButton');
        
        let isDebugPanelVisible = false;
        
        function toggleDebugPanel() {
            isDebugPanelVisible = !isDebugPanelVisible;
            console.log('Toggling debug panel:', isDebugPanelVisible);
            
            if (isDebugPanelVisible) {
                debugPanel.classList.remove('hidden');
                debugButton.classList.add('active');
                debugButton.textContent = 'Debug Panel (Active)';
            } else {
                debugPanel.classList.add('hidden');
                debugButton.classList.remove('active');
                debugButton.textContent = 'Debug Panel';
            }
        }
        
        function closeDebugPanel() {
            isDebugPanelVisible = false;
            debugPanel.classList.add('hidden');
            debugButton.classList.remove('active');
            debugButton.textContent = 'Debug Panel';
            console.log('Debug panel closed');
        }
        
        debugButton.addEventListener('click', toggleDebugPanel);
        closeButton.addEventListener('click', closeDebugPanel);
        
        console.log('Debug panel test initialized');
    </script>
</body>
</html>
